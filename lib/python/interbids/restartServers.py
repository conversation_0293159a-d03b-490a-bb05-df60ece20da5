# py3ready [RAVE-4507]
# $Header: /opt/Jeppesen/share/qfa/home/<USER>/mainline_git/main_cvs_repo/ccp_user/lib/python/interbids/restartServers.py,v 1.10 2023/07/25 23:16:15 ltr51 Exp $
"""
This script restarts all interbids filteringservers. If a new bidperiod extract 
is available, it will be loaded into the servers. The script retrieves which areas 
are filtering on which ports from the file CARMUSR/data/config/interbids_servers.conf. 
Xml-files are written for each area containing information about crew and latest 
extract, which are read in interbids web-application. Where the xml-files are written 
down are defined in the paths groupDataFolder and crewParentFolder.
Functions which need to be called from studio are defined in CustomInterbidsMethods.py
If a sever encounters an error when restarting, a mail will be sent containing 
a raised exception. Optional: email sent if all servers were started successfully. 
The mail address in both cases is defined in supportMailAddress. 
"""

from future import standard_library
standard_library.install_aliases()
from future.utils import raise_

import time
import os
import xmlrpc.client
from xmlrpc.client import ServerProxy
import glob
import getopt
import sys
import subprocess
import traceback
import datetime
import shlex

def getCarmDataPath(path):
    return os.path.expandvars("$CARMDATA/%s" % path)
 
def getCarmUserPath(path):
    return os.path.expandvars("$CARMUSR/%s" % path)

def getRosterbidsDataPath(path):
    return os.path.expandvars("$CARMDATA/ROSTERBIDS/%s" % path)
  
def createDir(path):
    try:
        os.makedirs(path)
    except Exception as err:
        return None
    
def findLatestBP(config):
    bpNbr = 0
    padWithZero = False    
    bpPath = getCarmDataPath("/LOCAL_PLAN/%s/*%s" % (config.fleet, config.base))
    listOfBPPaths = glob.glob(bpPath)
    for path in [p for p in listOfBPPaths if p.find("BP") >= 0]:
        indexInString = path.find("BP")
        bpNbrString = path[indexInString + 2:indexInString + 6].rstrip("_")
        if config.category:
            lpName = "IB_%s" % config.category
        else:
            lpName = "IB"
        lp_path = os.path.join(path, lpName)       
        if os.path.isdir(lp_path):
            if int(bpNbrString) > bpNbr and bpNbrString[0] == "0":
                bpNbr = int(bpNbrString)
                padWithZero = True
            elif int(bpNbrString) > bpNbr:
                bpNbr = int(bpNbrString)
                padWithZero = False
    
    if padWithZero:
        return "%s" % "0" + str(bpNbr)
    else:
        return str(bpNbr)
                                             
def restartServer(s, config, bpNbr):
    # Stop the server (if it's running)
    try:
        s.stopInterbidsServer()
    except:
        pass

    startStudioServer(s, config)
    
    print('loadPlanAndRuleset')
    loadPlanAndRuleset(s, config, bpNbr)
    
    print('areaSpecificSetup')
    areaSpecificSetup(s, config, bpNbr)
    
    fetchSimsAndCourses(s, config, bpNbr)
    
    if config.generateXml:
        generateXmlFiles(s, config, bpNbr)
        
    s.lockAndLoad()
   
def stopStudioServer(s, port): 
    try:  
        result = s.stopInterbidsServer()
        if result == "Ok":
            return "False"
    except Exception:
        print("Failed to stop Studio server on port: %s" % port)
        
    return "True"
 
    
def startStudioServer(s, config):
    path = getCarmUserPath("bin/startInterbidsServer.sh %s &" % config.port)
    os.system(path) 
    tryNum = 1
    while(True):
        if tryNum <= 6:
            print("Waiting for server to respond...")
            time.sleep(10)            
            try:
                s.getConfiguration()                
            except xmlrpc.client.Fault:
                break
            except Exception:
                pass
            tryNum = tryNum + 1
        else:
            raise Exception("Server failed to start")    

def loadPlanAndRuleset(s, config, bpNbr):
    if config.category:
        localPlan = "IB_%s" % config.category
    else:
        localPlan = "IB"
    planPath = "%s/BP%s_%s/%s/extract" % (config.fleet, bpNbr, config.base, localPlan)
    print('Loading plan...')
    try:
        s.loadPlan(planPath)
    #If a connection error occurs start the studio server again
    except ConnectionRefusedError:
        startStudioServer(s, config)
        s.loadPlan(planPath)
    except:
        raise_(Exception, "Failed to load plan: %s" % planPath)    
    
    environmentPath = "%s/BP%s_%s/%s/environment" % (config.fleet, bpNbr, config.base, localPlan) 
    print("Loading environment...")
    try:
        s.loadEnvironment(environmentPath)
    except:
        raise_(Exception, "Failed to load environment: %s" % environmentPath)      

def fetchSimsAndCourses(s, config, bpNbr):
    s.fetchTraining()
 
def areaSpecificSetup(s, config, bpNbr):       
    s.areaSpecificSetup()
            
def generateXmlFiles(s, config, bpNbr):
    #XML FILES FOR ROSTERBIDS
    print('\nCreating xml files for ROSTERBIDS...')

    rosterbidDataFolder = getRosterbidsDataPath("data")
    createDir(rosterbidDataFolder)
    print(f'Updating shared data in {rosterbidDataFolder}...')
    s.portData(rosterbidDataFolder, 'port_data.xml')
    s.acTypesData(rosterbidDataFolder, 'ac_types.xml')
    print(f'Creating key dates in {rosterbidDataFolder}...')
    s.key_dates(rosterbidDataFolder, bpNbr)

    crewFolder = getRosterbidsDataPath("crew")
    print(f'Creating crew info in {crewFolder}...')
    s.crew_info(crewFolder)

    print("Done creating xml\n")

#RAVE-2627: Update restartServers.py to send email
def sendEmail(subject_text, content_text, mailAddress):
    SENDMAIL = "/usr/lib/sendmail"
    p=os.popen(f"{SENDMAIL} -t", "w")
    p.write(f"To: {mailAddress}\n")
    p.write(f"Subject: Filter Servers Report - {subject_text}\n")
    p.write("\n")
    p.write(content_text)
    sts=p.close()

def sendErrorReport(fleet, base, server, email_text, err, mailAdress):
    contents = "Error %s" % email_text + " server %s,%s" % (fleet,base) + " process: %s \n" % server + "Error: %s" % err 
    sendEmail("failed", contents, mailAdress)

def sendSuccessEmail(email_text, mailAddress):
    contents = "%s: " % email_text
    sendEmail("successful", contents, mailAddress)
    
def readConfigFile():
    serverList = []
    serverProcessFile = open(getCarmUserPath('data/config/interbids_servers.conf'), 'r')
    for lineNr, line in enumerate(serverProcessFile.readlines()):
        line = line.lstrip()
        if not line or line.startswith('#'):
            continue
        try:
            server = ServerConfig()
            params = line.split(',')
            server.url = params[0].strip()
            hostAndPort = server.url.split('//')[1]
            server.host = hostAndPort.split(':')[0]
            server.port = int(hostAndPort.split(':')[1])
            server.fleet = params[1].strip()
            server.base = params[2].strip()
            server.category = params[3].strip()
            server.generateXml = params[4].strip().upper() == 'TRUE'
            serverList.append(server)
        except:
            raise Exception('Error parsing config file (line %d)' % lineNr)
    return serverList

def setEnv():
    try:
        command = shlex.split("bash -c 'source /opt/Jeppesen/share/qfa/etc/carmsite/CONFIG && env'")
        proc = subprocess.Popen(command, stdout = subprocess.PIPE, stderr=subprocess.PIPE)
        for line in iter(proc.stdout.readline,b''):
            cfg_line = line.decode('utf-8')[:-1]
            if "_SERVER=" in cfg_line:
                (key, _, value) = cfg_line.partition("=")
                os.environ[key] = value
        proc.communicate()
    except:
        raise Exception("Error in setEnv")
    
class ServerConfig(object):
  def __init__(self):
    self.url = None
    self.host = None
    self.port = None
    self.fleet = None
    self.base = None
    self.category = None
    self.generateXml = None
    
def main(*argv, **kwds):
    """
Usage:

    restartServers.py -a

    or

    restartServers.py [-g fleet,base]

    or

    restartServers.py -s

    or

    restartServers.py [-t fleet,base]

    or

    restartServers.py -c

    or

    restartServers.py --help


Arguments:
    -a               Restart all filter servers.

    -g fleet,base    Restart the filter servers for the specified fleet,base.
                     i.e. -g CCR_FCLH_B767,SYD or -g CCR_FCSH,*

    -s               Stop all the filter servers.

    -t fleet,base    Stop the filter servers for the specified fleet,base.
                     i.e. -t CCR_FCLH_B767,SYD or -t CCR_FCSH,*

    -c               Check if the filter servers are running and restart
                     the filter servers that are not running.

    -h               Print the help text.

    """
    #RAVE-2627: Update restartServers.py to send email
    supportMailAddress = "<EMAIL>"
    SEND_SUCCESS_EMAIL = False; #optional - hard-code here
    an_exception_occurred = False;
    host_found = False;
    
    restart_all = False
    fleet_to_process = "*"
    base_to_process = "*"
    category_to_process = "*"
    stop_servers = False
    stop_specific_server = False
    stop_fleet_to_process = "*"
    stop_base_to_process = "*"
    stop_category_to_process = "*"
    check_and_restart_if_req = False

    if len(argv) == 0:
        argv = sys.argv[1:]
    try:
        optlist, params = getopt.getopt(argv, 'hag:st:c',
                                        ["help",
                                         "all",
                                         "g=",
                                         "stop",
                                         "t=",
                                         "check"])
    except:
        print(main.__doc__)
        return
    
    for (opt, value) in optlist:
        if opt in ('-h', '--help'):
            print(main.__doc__)
            return
        elif opt in ('-a', '--all'):
            restart_all = True
        elif opt in ('-g'):
            fleet_to_process = value.split(',')[0]
            try:
                base_to_process = value.split(',')[1]
            except:
                pass
            try:
                category_to_process = value.split(',')[2]
            except:
                pass
            print("(%s) Restart with -g for %s, %s, %s" % (time.strftime('%d%b%Y %H%M%S'),
                                                           fleet_to_process,
                                                           base_to_process,
                                                           category_to_process))
            
        elif opt in ('-s', '--stop'):
            stop_servers = True

        elif opt in ('-t'):
            stop_specific_server = True
            stop_fleet_to_process = value.split(',')[0]
            try:
                stop_base_to_process = value.split(',')[1]
            except:
                pass
            try:
                stop_category_to_process = value.split(',')[2]
            except:
                pass
            print("(%s) Stop with -t for %s, %s, %s" % (time.strftime('%d%b%Y %H%M%S'),
                                                         stop_fleet_to_process,
                                                         stop_base_to_process,
                                                         stop_category_to_process))

        elif opt in ('-c', '--check'):
            check_and_restart_if_req = True


    if not (os.path.expandvars("$CARMUSR") != "$CARMUSR"):
        print("CARMUSR not set. Terminating.")
        return  

    if not (os.path.expandvars("$CARMDATA") != "$CARMDATA"):
        print("CARMDATA not set. Terminating.")
        return

    #if not (os.path.expandvars("$CARMSYS") != "$CARMSYS"):
    #    print "CARMSYS not set. Terminating."
    #    return
    
    #Restart servers
    thisHost = subprocess.getoutput('hostname')
    serverList = readConfigFile()
    for server in serverList:      
        if server.host != thisHost:
            continue
        host_found = True;
        bpNbr = findLatestBP(server)
        if bpNbr == "0":
            continue     
        s = ServerProxy(server.url)
        force_restart = False
        if check_and_restart_if_req:
            print("Checking the server response for fleet, base, category: %s, %s, %s on port %d on host %s..." \
                          % (server.fleet, server.base, server.category, server.port, server.host))
            try:
                conf = s.getConfiguration()
                print("Response OK.")
                continue
            except:
                timestamp = datetime.datetime.now().strftime("%d%b%Y %H:%M:%S")
                print("{} - BAD response, will be restarted.".format(timestamp))
                force_restart = True
        base_check = base_to_process == "*" or server.base == base_to_process
        fleet_check = fleet_to_process == "*" or server.fleet == fleet_to_process
        category_check = category_to_process == "*" or server.category == category_to_process

        # Check for specific server stop
        stop_base_check = stop_base_to_process == "*" or server.base == stop_base_to_process
        stop_fleet_check = stop_fleet_to_process == "*" or server.fleet == stop_fleet_to_process
        stop_category_check = stop_category_to_process == "*" or server.category == stop_category_to_process

        # Determine if this server should be processed
        should_restart = restart_all or force_restart or (base_check and fleet_check and category_check)
        should_stop_all = stop_servers
        should_stop_specific = stop_specific_server and (stop_base_check and stop_fleet_check and stop_category_check)

        if not (should_restart or should_stop_all or should_stop_specific):
            continue

        setEnv()
        if stop_servers or should_stop_specific:
            try:
                print("Stop servers for fleet, base, category: %s, %s, %s on port %d" % (server.fleet, server.base, server.category, server.port))
                stopStudioServer(s, server.port)
                continue
            except Exception as err:
                an_exception_occurred = True
                email_text = "stopping"
                sendErrorReport(server.fleet, server.base, server.url, email_text, err, supportMailAddress)
                print(err)
                continue
        else:
            print("Restart servers for fleet, base, category: %s, %s, %s on port %d" % (server.fleet, server.base, server.category, server.port))
            print("                                       BP: ", bpNbr)
            try:
                restartServer(s, server, bpNbr) 
            except Exception as err:
                an_exception_occurred = True
                email_text = "starting"
                sendErrorReport(server.fleet, server.base, server.url, email_text, err, supportMailAddress)
                traceback.print_exc()

    #RAVE-2627: Update restartServers.py to send email
    #report result
    if not host_found:
        sendEmail("failed", "Error accessing Filter Servers from host: %s\n\n"
                  "Your host must match: %s\n\n Change your host and try again" % (thisHost, server.host)
                  , supportMailAddress)
    elif SEND_SUCCESS_EMAIL and not an_exception_occurred:
        sendSuccessEmail("Filter Servers restarted successfully", supportMailAddress)
                           
if __name__=='carmen' or __name__=='__main__':
    sys.exit(main())
